#!/usr/bin/env python3
"""
Test script to check if all imports work correctly
"""

import sys
import traceback

def test_import(module_name, description):
    """Test importing a module"""
    try:
        __import__(module_name)
        print(f"✓ {description}")
        return True
    except Exception as e:
        print(f"✗ {description}: {e}")
        traceback.print_exc()
        return False

def main():
    """Test all critical imports"""
    print("Testing Stock AI Agents imports...")
    print("=" * 50)
    
    success_count = 0
    total_count = 0
    
    tests = [
        ("src.stock_ai.config", "Configuration"),
        ("src.stock_ai.database", "Database"),
        ("src.stock_ai.models", "Models"),
        ("src.stock_ai.tools", "Tools"),
        ("src.stock_ai.agents", "Agents"),
        ("src.stock_ai.services", "Services"),
        ("src.stock_ai.main", "Main application"),
    ]
    
    for module, description in tests:
        total_count += 1
        if test_import(module, description):
            success_count += 1
    
    print("=" * 50)
    print(f"Results: {success_count}/{total_count} imports successful")
    
    if success_count == total_count:
        print("🎉 All imports working correctly!")
        return 0
    else:
        print("❌ Some imports failed. Check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
