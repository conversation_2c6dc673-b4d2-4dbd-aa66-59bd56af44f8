"""
Configuration management for Stock AI Agents system
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration settings"""
    
    url: str = Field(default="postgresql://localhost:5432/stock_ai", env="DATABASE_URL")
    host: str = Field(default="localhost", env="DATABASE_HOST")
    port: int = Field(default=5432, env="DATABASE_PORT")
    name: str = Field(default="stock_ai", env="DATABASE_NAME")
    user: str = Field(default="postgres", env="DATABASE_USER")
    password: str = Field(default="", env="DATABASE_PASSWORD")
    
    class Config:
        env_prefix = "DATABASE_"


class OpenAISettings(BaseSettings):
    """OpenAI API configuration settings"""
    
    api_key: str = Field(..., env="OPENAI_API_KEY")
    base_url: str = Field(default="https://api.openai.com/v1", env="OPENAI_BASE_URL")
    model: str = Field(default="gpt-4", env="OPENAI_MODEL")
    max_tokens: int = Field(default=2000, env="OPENAI_MAX_TOKENS")
    temperature: float = Field(default=0.7, env="OPENAI_TEMPERATURE")
    
    class Config:
        env_prefix = "OPENAI_"


class TradingSettings(BaseSettings):
    """Trading configuration settings"""
    
    mode: str = Field(default="SIMULATION", env="TRADING_MODE")  # SIMULATION or LIVE
    initial_capital: float = Field(default=100000.0, env="INITIAL_CAPITAL")
    max_position_size: float = Field(default=0.1, env="MAX_POSITION_SIZE")
    stop_loss_percentage: float = Field(default=0.05, env="STOP_LOSS_PERCENTAGE")
    take_profit_percentage: float = Field(default=0.15, env="TAKE_PROFIT_PERCENTAGE")
    max_daily_loss: float = Field(default=0.02, env="MAX_DAILY_LOSS")
    max_drawdown: float = Field(default=0.1, env="MAX_DRAWDOWN")
    risk_free_rate: float = Field(default=0.03, env="RISK_FREE_RATE")
    
    class Config:
        env_prefix = "TRADING_"


class MarketDataSettings(BaseSettings):
    """Market data configuration settings"""
    
    provider: str = Field(default="yahoo", env="MARKET_DATA_PROVIDER")
    alpha_vantage_api_key: Optional[str] = Field(default=None, env="ALPHA_VANTAGE_API_KEY")
    update_interval: int = Field(default=300, env="UPDATE_INTERVAL")  # seconds
    
    class Config:
        env_prefix = "MARKET_DATA_"


class AgentSettings(BaseSettings):
    """Agent configuration settings"""
    
    update_interval: int = Field(default=60, env="AGENT_UPDATE_INTERVAL")  # seconds
    decision_cooldown: int = Field(default=300, env="DECISION_COOLDOWN")  # seconds
    max_concurrent_agents: int = Field(default=5, env="MAX_CONCURRENT_AGENTS")
    
    class Config:
        env_prefix = "AGENT_"


class MarketHoursSettings(BaseSettings):
    """Market hours configuration"""
    
    open_hour: int = Field(default=14, env="MARKET_OPEN_HOUR")  # UTC
    open_minute: int = Field(default=30, env="MARKET_OPEN_MINUTE")
    close_hour: int = Field(default=21, env="MARKET_CLOSE_HOUR")  # UTC
    close_minute: int = Field(default=0, env="MARKET_CLOSE_MINUTE")
    
    class Config:
        env_prefix = "MARKET_"


class LoggingSettings(BaseSettings):
    """Logging configuration settings"""
    
    level: str = Field(default="INFO", env="LOG_LEVEL")
    file: str = Field(default="logs/stock_ai.log", env="LOG_FILE")
    max_size: str = Field(default="10MB", env="LOG_MAX_SIZE")
    backup_count: int = Field(default=5, env="LOG_BACKUP_COUNT")
    
    class Config:
        env_prefix = "LOG_"


class NotificationSettings(BaseSettings):
    """Notification configuration settings"""
    
    enabled: bool = Field(default=True, env="ENABLE_NOTIFICATIONS")
    webhook_url: Optional[str] = Field(default=None, env="NOTIFICATION_WEBHOOK_URL")
    level: str = Field(default="WARNING", env="NOTIFICATION_LEVEL")
    
    class Config:
        env_prefix = "NOTIFICATION_"


class Settings(BaseSettings):
    """Main application settings"""
    
    debug: bool = Field(default=False, env="DEBUG")
    testing: bool = Field(default=False, env="TESTING")
    mock_market_data: bool = Field(default=False, env="MOCK_MARKET_DATA")
    
    # Sub-settings
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    openai: OpenAISettings = Field(default_factory=OpenAISettings)
    trading: TradingSettings = Field(default_factory=TradingSettings)
    market_data: MarketDataSettings = Field(default_factory=MarketDataSettings)
    agents: AgentSettings = Field(default_factory=AgentSettings)
    market_hours: MarketHoursSettings = Field(default_factory=MarketHoursSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    notifications: NotificationSettings = Field(default_factory=NotificationSettings)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Global settings instance
settings = Settings()
