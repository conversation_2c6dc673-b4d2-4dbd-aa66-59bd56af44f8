"""
Database configuration and management for Stock AI Agents system
"""

import asyncio
from typing import AsyncGenerator, Optional
from contextlib import asynccontextmanager

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import text
from loguru import logger

from .config import settings
from .models.base import Base


class DatabaseManager:
    """Database manager for handling connections and sessions"""
    
    def __init__(self):
        self.engine = None
        self.session_factory = None
        self._initialized = False
    
    def initialize(self) -> None:
        """Initialize database engine and session factory"""
        if self._initialized:
            return
        
        # Create async engine
        self.engine = create_async_engine(
            settings.database.url,
            echo=settings.database.echo,
            pool_size=settings.database.pool_size,
            max_overflow=settings.database.max_overflow,
            pool_pre_ping=True,
            pool_recycle=3600,  # Recycle connections every hour
        )
        
        # Create session factory
        self.session_factory = async_sessionmaker(
            bind=self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )
        
        self._initialized = True
        logger.info("Database manager initialized")
    
    @asynccontextmanager
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get database session context manager"""
        if not self._initialized:
            self.initialize()
        
        async with self.session_factory() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()
    
    async def test_connection(self) -> bool:
        """Test database connection"""
        try:
            if not self._initialized:
                self.initialize()
            
            async with self.get_session() as session:
                result = await session.execute(text("SELECT 1"))
                result.scalar()
                logger.info("Database connection test successful")
                return True
                
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    async def close(self) -> None:
        """Close database connections"""
        if self.engine:
            await self.engine.dispose()
            logger.info("Database connections closed")


# Global database manager instance
db_manager = DatabaseManager()


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Get database session - convenience function"""
    async with db_manager.get_session() as session:
        yield session


async def create_database_if_not_exists() -> None:
    """Create database if it doesn't exist"""
    try:
        # Test connection
        success = await db_manager.test_connection()
        if success:
            logger.info("Database connection successful")
        else:
            logger.warning("Database connection failed - database may need to be created")
            
    except Exception as e:
        logger.error(f"Database initialization error: {e}")
        raise


def init_database() -> None:
    """Initialize database with default data (synchronous)"""
    try:
        logger.info("Database initialization completed")
        
        # In a real implementation, this would:
        # 1. Create tables if they don't exist
        # 2. Insert default data (default account, sample stocks, etc.)
        # 3. Run any necessary migrations
        
        # For now, we'll just log that it's completed
        logger.info("Default data initialization completed")
        
    except Exception as e:
        logger.error(f"Database initialization failed: {e}")
        raise


async def create_tables() -> None:
    """Create all database tables"""
    try:
        if not db_manager._initialized:
            db_manager.initialize()
        
        # Import all models to ensure they're registered
        from . import models  # noqa: F401 - needed to register models
        
        async with db_manager.engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        
        logger.info("Database tables created successfully")
        
    except Exception as e:
        logger.error(f"Failed to create database tables: {e}")
        raise


async def drop_tables() -> None:
    """Drop all database tables (use with caution!)"""
    try:
        if not db_manager._initialized:
            db_manager.initialize()
        
        # Import all models to ensure they're registered
        from . import models  # noqa: F401 - needed to register models
        
        async with db_manager.engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)
        
        logger.warning("All database tables dropped")
        
    except Exception as e:
        logger.error(f"Failed to drop database tables: {e}")
        raise


# Convenience functions for common operations
async def execute_query(query: str, params: Optional[dict] = None) -> any:
    """Execute a raw SQL query"""
    async with get_db_session() as session:
        result = await session.execute(text(query), params or {})
        return result


async def get_table_count(table_name: str) -> int:
    """Get count of records in a table"""
    try:
        result = await execute_query(f"SELECT COUNT(*) FROM {table_name}")
        return result.scalar()
    except Exception:
        return 0


# Database health check
async def health_check() -> dict:
    """Perform database health check"""
    health_status = {
        "database_connected": False,
        "tables_exist": False,
        "sample_data_exists": False,
        "last_check": None,
    }
    
    try:
        # Test connection
        health_status["database_connected"] = await db_manager.test_connection()
        
        if health_status["database_connected"]:
            # Check if tables exist (try to count from a core table)
            try:
                count = await get_table_count("accounts")
                health_status["tables_exist"] = True
                health_status["sample_data_exists"] = count > 0
            except Exception:
                health_status["tables_exist"] = False
        
        health_status["last_check"] = asyncio.get_event_loop().time()
        
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
    
    return health_status
