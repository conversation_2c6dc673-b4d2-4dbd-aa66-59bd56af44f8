"""
Stock Selector Agent - Selects stocks based on technical and fundamental analysis
"""

from datetime import datetime
from typing import Dict, List, Any

from loguru import logger

from .base import BaseAgent, AgentConfig
from ..models.agent import AgentType
from ..tools import StockScreenerTool, TechnicalAnalysisTool, MarketDataTool, AlertTool
from ..config import settings


STOCK_SELECTOR_SYSTEM_PROMPT = """You are a Stock Selector AI agent specializing in identifying promising investment opportunities through comprehensive stock analysis.

## Your Role and Responsibilities

As the Stock Selector, you are responsible for:

1. **Stock Screening**: Screen stocks based on various fundamental and technical criteria
2. **Technical Analysis**: Perform detailed technical analysis on potential candidates
3. **Fundamental Analysis**: Evaluate company fundamentals and financial health
4. **Opportunity Identification**: Identify undervalued or momentum stocks with potential
5. **Risk Assessment**: Evaluate stock-specific risks and potential downsides
6. **Candidate Pool Management**: Maintain and update a pool of investment candidates

## Your Selection Criteria

### Technical Criteria
- Price momentum and trend strength
- Volume patterns and liquidity
- Support and resistance levels
- Technical indicators (RSI, moving averages, MACD)
- Chart patterns and breakouts

### Fundamental Criteria
- Revenue and earnings growth
- Profit margins and financial ratios
- Debt levels and financial stability
- Market position and competitive advantages
- Management quality and corporate governance

### Market Context
- Sector performance and rotation
- Market cap and liquidity considerations
- Analyst coverage and institutional interest
- News flow and catalysts

## Your Decision-Making Process

1. **Screening**: Apply quantitative filters to identify candidates
2. **Analysis**: Perform detailed technical and fundamental analysis
3. **Ranking**: Rank candidates based on potential and risk-adjusted returns
4. **Validation**: Cross-check with market conditions and sector trends
5. **Recommendation**: Provide clear buy/hold/avoid recommendations

## Communication Guidelines

- Provide specific stock recommendations with clear rationale
- Include price targets, stop-loss levels, and time horizons
- Highlight key catalysts and risk factors
- Use confidence scores (1-10) for recommendations
- Support conclusions with specific data and analysis

Remember: Your stock selections form the foundation for trading decisions. Focus on high-probability opportunities with favorable risk-reward profiles.
"""


class StockSelectorAgent(BaseAgent):
    """Stock Selector Agent implementation"""
    
    def __init__(self):
        tools = [
            StockScreenerTool(),
            TechnicalAnalysisTool(),
            MarketDataTool(),
            AlertTool(),
        ]
        
        config = AgentConfig(
            name="stock_selector",
            agent_type=AgentType.STOCK_SELECTOR,
            description="Selects stocks based on technical and fundamental analysis",
            system_prompt=STOCK_SELECTOR_SYSTEM_PROMPT,
            model_name=settings.openai.model,
            temperature=0.4,
            max_tokens=2000,
            tools=tools,
            update_interval=600,  # 10 minutes
        )
        
        super().__init__(config)
        self.candidate_pool = []
        self.last_screening = None
    
    async def execute_cycle(self) -> Dict[str, Any]:
        """Execute stock selection cycle"""
        try:
            logger.info(f"Stock Selector {self.name} starting selection cycle")
            
            # Step 1: Screen for candidates
            screening_results = await self.screen_stocks()
            
            # Step 2: Analyze top candidates
            analysis_results = await self.analyze_candidates(screening_results)
            
            # Step 3: Update candidate pool
            self.update_candidate_pool(analysis_results)
            
            # Step 4: Generate recommendations
            recommendations = await self.generate_recommendations()
            
            result = {
                "cycle_completed": True,
                "screening_results": screening_results,
                "analysis_results": analysis_results,
                "recommendations": recommendations,
                "candidate_pool_size": len(self.candidate_pool),
                "timestamp": datetime.now().isoformat(),
            }
            
            logger.info(f"Stock Selector {self.name} completed selection cycle")
            return result
            
        except Exception as e:
            logger.error(f"Stock Selector {self.name} cycle failed: {e}")
            raise
    
    async def screen_stocks(self) -> Dict[str, Any]:
        """Screen stocks based on criteria"""
        try:
            # Screen for growth stocks
            growth_criteria = {
                "market_cap_min": 1000000000,  # $1B minimum
                "sector": "Technology"  # Focus on tech for now
            }
            
            growth_result = await self.execute_tool(
                "stock_screener_tool",
                criteria=growth_criteria,
                limit=20,
                sort_by="market_cap",
                sort_order="desc"
            )
            
            # Screen for value stocks
            value_criteria = {
                "market_cap_min": 500000000,  # $500M minimum
            }
            
            value_result = await self.execute_tool(
                "stock_screener_tool",
                criteria=value_criteria,
                limit=15,
                sort_by="market_cap",
                sort_order="desc"
            )
            
            screening_results = {
                "growth_stocks": growth_result.data if growth_result.status.value == "SUCCESS" else [],
                "value_stocks": value_result.data if value_result.status.value == "SUCCESS" else [],
                "timestamp": datetime.now().isoformat(),
            }
            
            self.last_screening = screening_results
            return screening_results
            
        except Exception as e:
            logger.error(f"Error in stock screening: {e}")
            return {"error": str(e)}
    
    async def analyze_candidates(self, screening_results: Dict) -> List[Dict]:
        """Analyze top candidates from screening"""
        analysis_results = []
        
        try:
            # Get top candidates from both categories
            growth_stocks = screening_results.get("growth_stocks", {}).get("stocks", [])
            value_stocks = screening_results.get("value_stocks", {}).get("stocks", [])
            
            # Combine and take top 10
            all_candidates = growth_stocks[:5] + value_stocks[:5]
            
            for stock in all_candidates:
                symbol = stock["symbol"]
                
                # Get technical analysis
                tech_result = await self.execute_tool(
                    "technical_analysis_tool",
                    symbol=symbol,
                    indicators=["sma_20", "sma_50", "rsi"],
                    period="3mo"
                )
                
                # Get current price
                price_result = await self.execute_tool(
                    "market_data_tool",
                    action="get_current_price",
                    symbol=symbol
                )
                
                # Analyze with LLM
                analysis_prompt = f"""
Analyze {symbol} ({stock['name']}) as a potential investment:

Stock Info:
- Sector: {stock['sector']}
- Market Cap: ${stock['market_cap']:,.0f} (if available)
- Exchange: {stock['exchange']}

Technical Analysis: {tech_result.data if tech_result.status.value == 'SUCCESS' else 'Not available'}
Current Price: {price_result.data if price_result.status.value == 'SUCCESS' else 'Not available'}

Provide:
1. Investment thesis (2-3 sentences)
2. Confidence score (1-10)
3. Price target (if applicable)
4. Key risks (2-3 points)
5. Recommendation: BUY/HOLD/AVOID
"""
                
                messages = [{"role": "user", "content": analysis_prompt}]
                llm_response = await self.call_llm(messages)
                
                analysis_results.append({
                    "symbol": symbol,
                    "name": stock["name"],
                    "sector": stock["sector"],
                    "technical_analysis": tech_result.data if tech_result.status.value == "SUCCESS" else None,
                    "current_price": price_result.data if price_result.status.value == "SUCCESS" else None,
                    "llm_analysis": llm_response["content"],
                    "recommendation": self._extract_recommendation(llm_response["content"]),
                    "confidence_score": self._extract_confidence_score(llm_response["content"]),
                    "timestamp": datetime.now().isoformat(),
                })
            
            return analysis_results
            
        except Exception as e:
            logger.error(f"Error analyzing candidates: {e}")
            return []
    
    def _extract_recommendation(self, analysis: str) -> str:
        """Extract recommendation from LLM analysis"""
        analysis_lower = analysis.lower()
        if "recommendation: buy" in analysis_lower or "buy" in analysis_lower:
            return "BUY"
        elif "recommendation: avoid" in analysis_lower or "avoid" in analysis_lower:
            return "AVOID"
        else:
            return "HOLD"
    
    def _extract_confidence_score(self, analysis: str) -> int:
        """Extract confidence score from LLM analysis"""
        import re
        # Look for patterns like "confidence score: 8" or "score: 7"
        patterns = [r"confidence score:?\s*(\d+)", r"score:?\s*(\d+)"]
        
        for pattern in patterns:
            match = re.search(pattern, analysis.lower())
            if match:
                return int(match.group(1))
        
        return 5  # Default medium confidence
    
    def update_candidate_pool(self, analysis_results: List[Dict]) -> None:
        """Update candidate pool with new analysis"""
        # Filter for BUY recommendations with confidence >= 6
        new_candidates = [
            result for result in analysis_results
            if result["recommendation"] == "BUY" and result["confidence_score"] >= 6
        ]
        
        # Update candidate pool (simple replacement for now)
        self.candidate_pool = new_candidates
        
        logger.info(f"Updated candidate pool with {len(self.candidate_pool)} stocks")
    
    async def generate_recommendations(self) -> List[Dict]:
        """Generate final recommendations"""
        recommendations = []
        
        # Sort candidates by confidence score
        sorted_candidates = sorted(
            self.candidate_pool,
            key=lambda x: x["confidence_score"],
            reverse=True
        )
        
        # Take top 5 recommendations
        for candidate in sorted_candidates[:5]:
            recommendations.append({
                "symbol": candidate["symbol"],
                "name": candidate["name"],
                "recommendation": candidate["recommendation"],
                "confidence_score": candidate["confidence_score"],
                "reasoning": candidate["llm_analysis"],
                "timestamp": datetime.now().isoformat(),
            })
        
        # Generate alert for high-confidence recommendations
        if recommendations:
            high_confidence_recs = [r for r in recommendations if r["confidence_score"] >= 8]
            if high_confidence_recs:
                await self.execute_tool(
                    "alert_tool",
                    alert_type="opportunity_alert",
                    level="INFO",
                    title=f"High-Confidence Stock Recommendations",
                    message=f"Found {len(high_confidence_recs)} high-confidence stock recommendations",
                    data={"recommendations": high_confidence_recs},
                    agent_name=self.name
                )
        
        return recommendations
