"""
Main entry point for Stock AI Agents system
"""

import asyncio
import signal
import sys
from typing import Optional
from pathlib import Path

import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.live import Live
from rich.layout import Layout
from rich.text import Text
from loguru import logger

from .config import settings
from .database import create_database_if_not_exists, init_database
from .services import AgentManager, CoordinationService, SchedulerService
from .utils.logging import setup_logging
from .utils.display import create_status_display
from .terminal import run_interactive_terminal


app = typer.Typer(
    name="stock-ai",
    help="Stock AI Agents - Multi-agent stock trading system",
    add_completion=False
)

console = Console()

# Global variables for services
agent_manager: Optional[AgentManager] = None
coordination_service: Optional[CoordinationService] = None
scheduler_service: Optional[SchedulerService] = None
shutdown_event = asyncio.Event()


def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received signal {signum}, initiating shutdown...")
    shutdown_event.set()


@app.command()
def init_db():
    """Initialize the database with default data"""
    try:
        console.print("[bold blue]Initializing database...[/bold blue]")
        
        # Run database initialization
        asyncio.run(create_database_if_not_exists())
        init_database()
        
        console.print("[bold green]✓ Database initialized successfully![/bold green]")
        
    except Exception as e:
        console.print(f"[bold red]✗ Database initialization failed: {e}[/bold red]")
        raise typer.Exit(1)


@app.command()
def start(
    mode: str = typer.Option("collaborative", help="Coordination mode: autonomous, collaborative, hierarchical"),
    debug: bool = typer.Option(False, help="Enable debug mode"),
    mock_data: bool = typer.Option(False, help="Use mock market data"),
):
    """Start the Stock AI Agents system"""
    try:
        # Setup logging
        setup_logging(debug=debug)

        # Set configuration
        if mock_data:
            settings.mock_market_data = True

        # Run the main application
        asyncio.run(main_async(mode))

    except KeyboardInterrupt:
        console.print("\n[yellow]Shutdown requested by user[/yellow]")
    except Exception as e:
        console.print(f"[bold red]✗ System failed: {e}[/bold red]")
        logger.exception("System failure")
        raise typer.Exit(1)


@app.command()
def interactive(
    debug: bool = typer.Option(False, help="Enable debug mode"),
    mock_data: bool = typer.Option(False, help="Use mock market data"),
):
    """Start the interactive terminal interface"""
    try:
        # Setup logging
        setup_logging(debug=debug)

        # Set configuration
        if mock_data:
            settings.mock_market_data = True

        # Run interactive terminal
        asyncio.run(run_interactive_terminal())

    except KeyboardInterrupt:
        console.print("\n[yellow]Exiting interactive mode[/yellow]")
    except Exception as e:
        console.print(f"[bold red]✗ Interactive mode failed: {e}[/bold red]")
        logger.exception("Interactive mode failure")
        raise typer.Exit(1)


async def main_async(coordination_mode: str = "collaborative"):
    """Main async application"""
    global agent_manager, coordination_service, scheduler_service

    try:
        # Setup signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        console.print(Panel.fit(
            "[bold blue]Stock AI Agents System[/bold blue]\n"
            "Multi-agent stock trading system with AI-powered decision making",
            title="🤖 Starting System",
            border_style="blue"
        ))

        # Initialize database
        console.print("[dim]Checking database connection...[/dim]")
        await create_database_if_not_exists()

        # Initialize services
        console.print("[dim]Initializing services...[/dim]")
        agent_manager = AgentManager()
        await agent_manager.initialize()

        coordination_service = CoordinationService(agent_manager)
        scheduler_service = SchedulerService(agent_manager, coordination_service)

        # Set coordination mode
        if coordination_mode.upper() in ["AUTONOMOUS", "COLLABORATIVE", "HIERARCHICAL"]:
            from .services.coordination_service import CoordinationMode
            mode = CoordinationMode(coordination_mode.upper())
            coordination_service.set_coordination_mode(mode)

        # Start services
        console.print("[dim]Starting agents...[/dim]")
        await agent_manager.start_all_agents()

        console.print("[dim]Starting coordination service...[/dim]")
        await coordination_service.start()

        console.print("[dim]Starting scheduler service...[/dim]")
        await scheduler_service.start()

        console.print("[bold green]✓ System started successfully![/bold green]")
        console.print(f"[dim]Coordination mode: {coordination_mode.upper()}[/dim]")
        console.print(f"[dim]Trading mode: {settings.trading.mode}[/dim]")

        # Start status display
        await run_with_status_display()

    except Exception as e:
        logger.exception("Failed to start system")
        console.print(f"[bold red]✗ Failed to start system: {e}[/bold red]")
        raise

    finally:
        # Cleanup
        await cleanup_services()


async def run_with_status_display():
    """Run system with live status display"""
    global agent_manager, coordination_service

    try:
        with Live(create_status_display(agent_manager, coordination_service),
                 refresh_per_second=1, console=console) as live:

            # Main loop
            while not shutdown_event.is_set():
                try:
                    # Update display
                    live.update(create_status_display(agent_manager, coordination_service))

                    # Wait for shutdown or next update
                    await asyncio.wait_for(shutdown_event.wait(), timeout=1.0)
                    break

                except asyncio.TimeoutError:
                    continue
                except KeyboardInterrupt:
                    break

    except Exception as e:
        logger.exception("Error in status display")
        console.print(f"[bold red]Status display error: {e}[/bold red]")


async def cleanup_services():
    """Cleanup services on shutdown"""
    global agent_manager, coordination_service, scheduler_service

    console.print("\n[yellow]Shutting down system...[/yellow]")

    try:
        if scheduler_service:
            await scheduler_service.stop()
            console.print("[dim]✓ Scheduler service stopped[/dim]")

        if coordination_service:
            await coordination_service.stop()
            console.print("[dim]✓ Coordination service stopped[/dim]")

        if agent_manager:
            await agent_manager.shutdown()
            console.print("[dim]✓ Agent manager stopped[/dim]")

        console.print("[bold green]✓ System shutdown complete[/bold green]")

    except Exception as e:
        logger.exception("Error during cleanup")
        console.print(f"[bold red]✗ Cleanup error: {e}[/bold red]")


@app.command()
def status():
    """Show system status"""
    try:
        console.print("[bold blue]Stock AI Agents - System Status[/bold blue]\n")
        
        # This would connect to running system to get status
        # For now, show configuration
        table = Table(title="Configuration")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="green")
        
        table.add_row("Trading Mode", settings.trading.mode)
        table.add_row("Initial Capital", f"${settings.trading.initial_capital:,.2f}")
        table.add_row("Max Position Size", f"{settings.trading.max_position_size * 100:.1f}%")
        table.add_row("Stop Loss", f"{settings.trading.stop_loss_percentage * 100:.1f}%")
        table.add_row("Take Profit", f"{settings.trading.take_profit_percentage * 100:.1f}%")
        table.add_row("Database URL", settings.database.url)
        table.add_row("Log Level", settings.logging.level)
        
        console.print(table)
        
    except Exception as e:
        console.print(f"[bold red]✗ Failed to get status: {e}[/bold red]")
        raise typer.Exit(1)


@app.command()
def test_db():
    """Test database connection"""
    try:
        console.print("[bold blue]Testing database connection...[/bold blue]")
        
        from .database import db_manager
        
        async def test_connection():
            result = await db_manager.test_connection()
            return result
        
        result = asyncio.run(test_connection())
        
        if result:
            console.print("[bold green]✓ Database connection successful![/bold green]")
        else:
            console.print("[bold red]✗ Database connection failed![/bold red]")
            raise typer.Exit(1)
        
    except Exception as e:
        console.print(f"[bold red]✗ Database test failed: {e}[/bold red]")
        raise typer.Exit(1)


@app.command()
def create_sample_data():
    """Create sample data for testing"""
    try:
        console.print("[bold blue]Creating sample data...[/bold blue]")
        
        # This would create sample market data, transactions, etc.
        console.print("[dim]Creating sample stocks...[/dim]")
        console.print("[dim]Creating sample market data...[/dim]")
        console.print("[dim]Creating sample account data...[/dim]")
        
        console.print("[bold green]✓ Sample data created successfully![/bold green]")
        
    except Exception as e:
        console.print(f"[bold red]✗ Failed to create sample data: {e}[/bold red]")
        raise typer.Exit(1)


def main():
    """Main entry point"""
    try:
        app()
    except Exception as e:
        console.print(f"[bold red]Application error: {e}[/bold red]")
        sys.exit(1)


if __name__ == "__main__":
    main()
