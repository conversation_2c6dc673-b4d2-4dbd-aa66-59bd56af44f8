"""
Market data related database models
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional

from sqlalchemy import (
    Column, String, Numeric, DateTime, Boolean, 
    Text, JSON, Integer, Enum as SQLEnum
)
from sqlalchemy.orm import relationship

from .base import BaseModel


class MarketStatus(str, Enum):
    """Market status"""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PRE_MARKET = "PRE_MARKET"
    AFTER_HOURS = "AFTER_HOURS"
    HOLIDAY = "HOLIDAY"


class Stock(BaseModel):
    """Stock information model"""
    
    __tablename__ = "stocks"
    
    symbol = Column(String(20), nullable=False, unique=True, index=True)
    name = Column(String(200), nullable=False)
    exchange = Column(String(50), nullable=False)
    sector = Column(String(100), nullable=True)
    industry = Column(String(100), nullable=True)
    
    # Basic info
    market_cap = Column(Numeric(20, 2), nullable=True)
    shares_outstanding = Column(Numeric(20, 0), nullable=True)
    currency = Column(String(10), default="USD", nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_tradable = Column(Boolean, default=True, nullable=False)
    
    # Additional info
    description = Column(Text, nullable=True)
    website = Column(String(200), nullable=True)
    
    # Relationships
    market_data = relationship("MarketData", back_populates="stock", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Stock(symbol='{self.symbol}', name='{self.name}')>"


class MarketData(BaseModel):
    """Market data model for stocks"""
    
    __tablename__ = "market_data"
    
    stock_id = Column(Integer, ForeignKey("stocks.id"), nullable=False)
    timestamp = Column(DateTime(timezone=True), nullable=False, index=True)
    
    # OHLCV data
    open_price = Column(Numeric(10, 4), nullable=False)
    high_price = Column(Numeric(10, 4), nullable=False)
    low_price = Column(Numeric(10, 4), nullable=False)
    close_price = Column(Numeric(10, 4), nullable=False)
    volume = Column(Numeric(20, 0), nullable=False)
    
    # Additional data
    adjusted_close = Column(Numeric(10, 4), nullable=True)
    dividend_amount = Column(Numeric(10, 4), nullable=True)
    split_coefficient = Column(Numeric(10, 4), nullable=True)
    
    # Technical indicators (can be calculated)
    sma_20 = Column(Numeric(10, 4), nullable=True)  # 20-day Simple Moving Average
    sma_50 = Column(Numeric(10, 4), nullable=True)  # 50-day Simple Moving Average
    rsi = Column(Numeric(6, 2), nullable=True)      # Relative Strength Index
    
    # Data source and quality
    data_source = Column(String(50), nullable=False, default="yahoo")
    data_quality = Column(String(20), default="GOOD", nullable=False)  # GOOD, FAIR, POOR
    
    # Relationships
    stock = relationship("Stock", back_populates="market_data")
    
    def __repr__(self) -> str:
        return f"<MarketData(stock_id={self.stock_id}, close={self.close_price}, timestamp={self.timestamp})>"


class TradingSession(BaseModel):
    """Trading session information"""
    
    __tablename__ = "trading_sessions"
    
    date = Column(DateTime(timezone=True), nullable=False, unique=True, index=True)
    market_status = Column(SQLEnum(MarketStatus), nullable=False)
    
    # Session times (all in UTC)
    pre_market_start = Column(DateTime(timezone=True), nullable=True)
    market_open = Column(DateTime(timezone=True), nullable=False)
    market_close = Column(DateTime(timezone=True), nullable=False)
    after_hours_end = Column(DateTime(timezone=True), nullable=True)
    
    # Session statistics
    total_volume = Column(Numeric(20, 0), nullable=True)
    advancing_stocks = Column(Integer, nullable=True)
    declining_stocks = Column(Integer, nullable=True)
    unchanged_stocks = Column(Integer, nullable=True)
    
    # Market indices (optional)
    sp500_open = Column(Numeric(10, 2), nullable=True)
    sp500_close = Column(Numeric(10, 2), nullable=True)
    nasdaq_open = Column(Numeric(10, 2), nullable=True)
    nasdaq_close = Column(Numeric(10, 2), nullable=True)
    dow_open = Column(Numeric(10, 2), nullable=True)
    dow_close = Column(Numeric(10, 2), nullable=True)
    
    # Notes
    notes = Column(Text, nullable=True)  # Holiday info, special events, etc.
    
    def __repr__(self) -> str:
        return f"<TradingSession(date={self.date.date()}, status={self.market_status})>"
