"""
Database migration and initialization utilities
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Dict, Any

from sqlalchemy import text
from loguru import logger

from ..models import Base, Account, Agent, Stock, TradingSession
from ..models.agent import AgentType, AgentStatus
from ..models.market import MarketStatus
from .connection import db_manager


def create_tables():
    """Create all database tables"""
    try:
        db_manager.initialize()
        Base.metadata.create_all(bind=db_manager.sync_engine)
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create tables: {e}")
        raise


def drop_tables():
    """Drop all database tables"""
    try:
        db_manager.initialize()
        Base.metadata.drop_all(bind=db_manager.sync_engine)
        logger.info("Database tables dropped successfully")
    except Exception as e:
        logger.error(f"Failed to drop tables: {e}")
        raise


def init_database():
    """Initialize database with default data"""
    try:
        create_tables()
        _create_default_account()
        _create_default_agents()
        _create_sample_stocks()
        _create_current_trading_session()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


def _create_default_account():
    """Create default trading account"""
    session = db_manager.get_sync_session()
    try:
        # Check if default account exists
        existing_account = session.query(Account).filter_by(name="default").first()
        if existing_account:
            logger.info("Default account already exists")
            return
        
        # Create default account
        account = Account(
            name="default",
            account_type="SIMULATION",
            initial_capital=Decimal("100000.00"),
            current_cash=Decimal("100000.00"),
            total_value=Decimal("100000.00"),
            max_position_size=Decimal("0.1"),
            stop_loss_percentage=Decimal("0.05"),
            take_profit_percentage=Decimal("0.15"),
        )
        
        session.add(account)
        session.commit()
        logger.info("Created default account with $100,000 initial capital")
        
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to create default account: {e}")
        raise
    finally:
        session.close()


def _create_default_agents():
    """Create default agents"""
    session = db_manager.get_sync_session()
    try:
        # Agent configurations
        agents_config = [
            {
                "name": "market_analyst",
                "agent_type": AgentType.MARKET_ANALYST,
                "description": "Analyzes market trends and economic indicators",
                "system_prompt": """You are a Market Analyst AI agent specializing in stock market analysis.

Your responsibilities:
1. Analyze market trends and patterns
2. Monitor economic indicators and news
3. Assess market sentiment and volatility
4. Identify potential market opportunities and risks
5. Provide market context for other agents' decisions

You have access to market data, news feeds, and economic indicators. Use this information to provide comprehensive market analysis and insights.

Always provide data-driven analysis with clear reasoning and confidence levels.""",
                "tools_config": ["market_data_tool", "news_analysis_tool", "economic_indicators_tool"]
            },
            {
                "name": "stock_selector",
                "agent_type": AgentType.STOCK_SELECTOR,
                "description": "Selects stocks based on technical and fundamental analysis",
                "system_prompt": """You are a Stock Selector AI agent specializing in stock selection and analysis.

Your responsibilities:
1. Perform technical analysis on stocks
2. Conduct fundamental analysis of companies
3. Screen stocks based on various criteria
4. Maintain and update candidate stock pools
5. Evaluate stock investment potential and risks

You have access to stock data, financial statements, and technical indicators. Use comprehensive analysis to identify promising investment opportunities.

Always provide detailed analysis with clear buy/sell/hold recommendations and risk assessments.""",
                "tools_config": ["stock_screener_tool", "technical_analysis_tool", "fundamental_analysis_tool"]
            },
            {
                "name": "trading_decision_maker",
                "agent_type": AgentType.TRADING_DECISION_MAKER,
                "description": "Makes specific buy/sell trading decisions",
                "system_prompt": """You are a Trading Decision Maker AI agent responsible for executing trading decisions.

Your responsibilities:
1. Make specific buy/sell/hold decisions
2. Determine optimal entry and exit points
3. Set stop-loss and take-profit levels
4. Manage position sizing and risk
5. Execute trades based on analysis from other agents

You must record detailed reasoning for every trading decision. Consider market conditions, technical indicators, and risk management principles.

Always provide clear decision rationale with specific price targets and risk parameters.""",
                "tools_config": ["trading_tool", "position_sizing_tool", "risk_management_tool", "decision_recorder_tool"]
            },
            {
                "name": "portfolio_manager",
                "agent_type": AgentType.PORTFOLIO_MANAGER,
                "description": "Manages overall portfolio allocation and performance",
                "system_prompt": """You are a Portfolio Manager AI agent responsible for overall portfolio management.

Your responsibilities:
1. Optimize asset allocation across positions
2. Monitor portfolio performance and risk metrics
3. Rebalance portfolio when necessary
4. Ensure diversification and risk limits
5. Track portfolio against benchmarks and goals

You have access to portfolio data, performance metrics, and risk analytics. Use this information to maintain an optimal portfolio structure.

Always consider risk-adjusted returns and maintain proper diversification.""",
                "tools_config": ["portfolio_analysis_tool", "rebalancing_tool", "performance_tracking_tool"]
            },
            {
                "name": "risk_controller",
                "agent_type": AgentType.RISK_CONTROLLER,
                "description": "Monitors and controls investment risks",
                "system_prompt": """You are a Risk Controller AI agent responsible for risk management and control.

Your responsibilities:
1. Monitor portfolio and position-level risks
2. Enforce risk limits and stop-loss rules
3. Provide risk warnings and alerts
4. Assess correlation and concentration risks
5. Implement risk mitigation strategies

You have access to risk metrics, volatility data, and correlation analysis. Use this information to protect the portfolio from excessive losses.

Always prioritize capital preservation and risk-adjusted returns over absolute returns.""",
                "tools_config": ["risk_analysis_tool", "volatility_tool", "correlation_tool", "alert_tool"]
            }
        ]
        
        for agent_config in agents_config:
            # Check if agent already exists
            existing_agent = session.query(Agent).filter_by(name=agent_config["name"]).first()
            if existing_agent:
                logger.info(f"Agent {agent_config['name']} already exists")
                continue
            
            # Create agent
            agent = Agent(
                name=agent_config["name"],
                agent_type=agent_config["agent_type"],
                status=AgentStatus.ACTIVE,
                description=agent_config["description"],
                system_prompt=agent_config["system_prompt"],
                model_name="gpt-4",
                temperature="0.7",
                max_tokens=2000,
                tools_config=agent_config["tools_config"],
            )
            
            session.add(agent)
            logger.info(f"Created agent: {agent_config['name']}")
        
        session.commit()
        logger.info("Created default agents")
        
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to create default agents: {e}")
        raise
    finally:
        session.close()


def _create_sample_stocks():
    """Create sample stocks for testing"""
    session = db_manager.get_sync_session()
    try:
        # Sample stocks
        sample_stocks = [
            {"symbol": "AAPL", "name": "Apple Inc.", "exchange": "NASDAQ", "sector": "Technology", "industry": "Consumer Electronics"},
            {"symbol": "MSFT", "name": "Microsoft Corporation", "exchange": "NASDAQ", "sector": "Technology", "industry": "Software"},
            {"symbol": "GOOGL", "name": "Alphabet Inc.", "exchange": "NASDAQ", "sector": "Technology", "industry": "Internet Services"},
            {"symbol": "AMZN", "name": "Amazon.com Inc.", "exchange": "NASDAQ", "sector": "Consumer Discretionary", "industry": "E-commerce"},
            {"symbol": "TSLA", "name": "Tesla Inc.", "exchange": "NASDAQ", "sector": "Consumer Discretionary", "industry": "Electric Vehicles"},
            {"symbol": "NVDA", "name": "NVIDIA Corporation", "exchange": "NASDAQ", "sector": "Technology", "industry": "Semiconductors"},
            {"symbol": "META", "name": "Meta Platforms Inc.", "exchange": "NASDAQ", "sector": "Technology", "industry": "Social Media"},
            {"symbol": "JPM", "name": "JPMorgan Chase & Co.", "exchange": "NYSE", "sector": "Financial Services", "industry": "Banking"},
            {"symbol": "JNJ", "name": "Johnson & Johnson", "exchange": "NYSE", "sector": "Healthcare", "industry": "Pharmaceuticals"},
            {"symbol": "V", "name": "Visa Inc.", "exchange": "NYSE", "sector": "Financial Services", "industry": "Payment Processing"},
        ]
        
        for stock_data in sample_stocks:
            # Check if stock already exists
            existing_stock = session.query(Stock).filter_by(symbol=stock_data["symbol"]).first()
            if existing_stock:
                continue
            
            # Create stock
            stock = Stock(
                symbol=stock_data["symbol"],
                name=stock_data["name"],
                exchange=stock_data["exchange"],
                sector=stock_data["sector"],
                industry=stock_data["industry"],
                currency="USD",
                is_active=True,
                is_tradable=True,
            )
            
            session.add(stock)
        
        session.commit()
        logger.info("Created sample stocks")
        
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to create sample stocks: {e}")
        raise
    finally:
        session.close()


def _create_current_trading_session():
    """Create current trading session"""
    session = db_manager.get_sync_session()
    try:
        today = datetime.now().date()
        
        # Check if today's session exists
        existing_session = session.query(TradingSession).filter(
            TradingSession.date >= datetime.combine(today, datetime.min.time()),
            TradingSession.date < datetime.combine(today, datetime.max.time())
        ).first()
        
        if existing_session:
            logger.info("Today's trading session already exists")
            return
        
        # Create today's trading session
        trading_session = TradingSession(
            date=datetime.combine(today, datetime.min.time()),
            market_status=MarketStatus.CLOSED,  # Will be updated by market data service
            market_open=datetime.combine(today, datetime.min.time().replace(hour=14, minute=30)),  # 9:30 AM EST
            market_close=datetime.combine(today, datetime.min.time().replace(hour=21, minute=0)),   # 4:00 PM EST
        )
        
        session.add(trading_session)
        session.commit()
        logger.info("Created current trading session")
        
    except Exception as e:
        session.rollback()
        logger.error(f"Failed to create trading session: {e}")
        raise
    finally:
        session.close()
