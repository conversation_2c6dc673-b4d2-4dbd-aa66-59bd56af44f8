# Stock AI Agents - 炒股群组智能体系统

一个基于多智能体架构的股票交易系统，通过AI驱动的决策制定来最大化投资收益。

## 系统特性

- **多智能体协作**: 包含选股、买卖决策、市场分析等专业化智能体
- **持续运行**: 自动化交易决策，无需人工干预
- **智能暂停**: 支持市场时间感知的暂停/恢复机制
- **决策记录**: 完整记录所有交易决策的依据和过程
- **动态计划**: 智能体可以动态调整投资策略和计划
- **彩色终端**: 丰富的终端界面，支持彩色输出和状态监控

## 智能体角色

### 1. 市场分析智能体 (Market Analyst)
- 分析市场趋势和宏观经济指标
- 识别市场机会和风险
- 提供市场情绪分析

### 2. 选股智能体 (Stock Selector)
- 基于技术分析和基本面分析选择股票
- 评估股票的投资价值和风险
- 维护候选股票池

### 3. 买卖决策智能体 (Trading Decision Maker)
- 制定具体的买卖决策
- 确定交易时机和数量
- 管理风险和止损策略

### 4. 投资组合管理智能体 (Portfolio Manager)
- 管理整体投资组合
- 优化资产配置
- 监控投资组合表现

### 5. 风险控制智能体 (Risk Controller)
- 监控投资风险
- 执行风险控制策略
- 提供风险预警

## 技术架构

- **编程语言**: Python 3.9+
- **AI模型**: OpenAI兼容接口
- **数据库**: PostgreSQL
- **工具支持**: MCP协议 + 自定义工具
- **异步框架**: asyncio + aiohttp
- **终端界面**: Rich + Colorama

## 项目结构

```
stock_ai/
├── src/stock_ai/           # 主要源代码
│   ├── agents/            # 智能体实现
│   ├── tools/             # 工具集合
│   ├── database/          # 数据库相关
│   ├── models/            # 数据模型
│   ├── services/          # 业务服务
│   ├── utils/             # 工具函数
│   └── main.py           # 主入口
├── tests/                 # 测试代码
├── docs/                  # 文档
├── scripts/               # 脚本文件
├── data/                  # 数据文件
│   ├── decisions/         # 决策记录(MD文件)
│   └── plans/            # 投资计划(MD文件)
└── config/               # 配置文件
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd stock_ai

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -e .
```

### 2. 数据库配置

```bash
# 安装PostgreSQL并创建数据库
createdb stock_ai

# 运行数据库迁移
alembic upgrade head
```

### 3. 配置环境变量

```bash
cp .env.example .env
# 编辑.env文件，配置OpenAI API密钥和数据库连接
```

### 4. 运行系统

```bash
stock-ai start
```

## 配置说明

系统支持通过环境变量或配置文件进行配置：

- `OPENAI_API_KEY`: OpenAI API密钥
- `DATABASE_URL`: PostgreSQL数据库连接URL
- `LOG_LEVEL`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `TRADING_MODE`: 交易模式 (SIMULATION, LIVE)

## 开发指南

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_agents.py

# 运行覆盖率测试
pytest --cov=stock_ai
```

### 代码格式化

```bash
# 格式化代码
black src/ tests/
isort src/ tests/

# 类型检查
mypy src/
```

## 许可证

MIT License
